model Service {
  id                  Int               @id @default(autoincrement())
  name                String
  rate                Float
  rateUSD             Float             @default(0) // Store price in USD for conversion
  min_order           Int
  max_order           Int
  avg_time            String
  description         String            @db.Text
  updateText          String?           @db.Text
  userId              Int
  categoryId          Int
  favrouteCatId       Int?
  status              String            @default("active")
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt
  perqty              Int               @default(1000)
  serviceTypeId       Int?
  cancel              Boolean           @default(false)
  mode                String            @default("manual")
  refill              Boolean           @default(false)
  refillDays          Int?              @default(30)
  serviceSpeed        String?           @default("medium")
  personalizedService Boolean?          @default(false)
  refillDisplay       Int?              @default(24)
  favoriteServices    FavoriteService[]
  newOrders           NewOrder[]
  category            Category          @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  favrouteCat         FavrouteCat?      @relation(fields: [favrouteCatId], references: [id], onDelete: Cascade)
  serviceType         ServiceType?      @relation(fields: [serviceTypeId], references: [id])
  user                User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([categoryId], map: "Service_categoryId_fkey")
  @@index([favrouteCatId], map: "Service_favrouteCatId_fkey")
  @@index([serviceTypeId], map: "Service_serviceTypeId_idx")
  @@index([status], map: "Service_status_idx")
  @@index([userId], map: "Service_userId_idx")
  @@map("service")
}
