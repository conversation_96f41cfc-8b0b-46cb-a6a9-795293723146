{"name": "smm-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^2.8.0", "@hookform/resolvers": "^4.1.3", "@prisma/client": "^6.12.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.0", "@reduxjs/toolkit": "^2.6.1", "aos": "^2.3.4", "axios": "^1.8.4", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "framer-motion": "^12.12.1", "html-react-parser": "^5.2.2", "jodit-react": "^5.2.18", "lucide-react": "^0.479.0", "moment": "^2.30.1", "mysql2": "^3.14.2", "next": "15.2.2", "next-auth": "^5.0.0-beta.25", "next-themes": "^0.4.6", "nextjs-toploader": "^3.7.15", "nodemailer": "^6.10.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-google-button": "^0.8.0", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-slick": "^0.30.3", "react-spinners": "^0.15.0", "slick-carousel": "^1.8.1", "sonner": "^2.0.1", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/aos": "^3.0.7", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/node": "20.17.57", "@types/nodemailer": "^6.4.17", "@types/react": "19.1.6", "@types/react-dom": "^19", "@types/react-slick": "^0.23.13", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.2.2", "postcss": "^8.5.3", "prisma": "^6.8.2", "tailwindcss": "^3.4.17", "typescript": "5.8.3"}, "prisma": {"schema": "prisma/schema"}, "pnpm": {"ignoredBuiltDependencies": ["@prisma/engines", "@tailwindcss/oxide", "bcrypt", "prisma", "sharp", "unrs-resolver"], "onlyBuiltDependencies": ["@prisma/client"]}}