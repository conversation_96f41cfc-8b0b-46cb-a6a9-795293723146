-- Create Provider table manually
USE smm_panel_db;

CREATE TABLE IF NOT EXISTS `providers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `api_key` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `login_user` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `login_pass` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'inactive',
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  <PERSON><PERSON>AR<PERSON> (`id`),
  UNIQUE KEY `providers_name_key` (`name`),
  KEY `providers_status_idx` (`status`),
  KEY `providers_name_idx` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Check if table was created
SHOW TABLES LIKE 'providers';
DESCRIBE providers;
