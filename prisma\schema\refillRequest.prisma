model RefillRequest {
  id              Int       @id @default(autoincrement())
  orderId         Int
  userId          Int
  reason          String
  status          String    @default("pending")
  adminNotes      String?
  processedBy     Int?
  processedAt     DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  order           NewOrder  @relation(fields: [orderId], references: [id])
  processedByUser User?     @relation("ProcessedRefillRequests", fields: [processedBy], references: [id])
  user            User      @relation(fields: [userId], references: [id])

  @@index([processedBy], map: "RefillRequest_processedBy_fkey")
  @@index([createdAt], map: "RefillRequest_createdAt_idx")
  @@index([orderId], map: "RefillRequest_orderId_idx")
  @@index([status], map: "RefillRequest_status_idx")
  @@index([userId], map: "RefillRequest_userId_idx")
  @@map("refillrequest")
}
