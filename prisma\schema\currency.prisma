model CurrencySettings {
  id                 Int      @id @default(autoincrement())
  defaultCurrency    String   @default("USD")
  displayDecimals    Int      @default(2)
  currencyPosition   String   @default("left")
  thousandsSeparator String   @default(",")
  decimalSeparator   String   @default(".")
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  @@map("currency_settings")
}

model Currency {
  id        Int      @id @default(autoincrement())
  code      String   @unique @db.VarChar(3)
  name      String   @db.VarChar(100)
  symbol    String   @db.VarChar(10)
  rate      Decimal  @default(1.0000) @db.Decimal(10, 4)
  enabled   Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("currencies")
}
