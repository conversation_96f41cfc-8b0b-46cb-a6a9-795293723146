model Category {
  id            Int              @id @default(autoincrement())
  category_name String
  status        String           @default("active")
  userId        Int
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  hideCategory  String           @default("no")
  position      CategoryPosition @default(bottom)
  user          User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  newOrders     NewOrder[]
  services      Service[]

  @@index([userId], map: "Category_userId_fkey")
  @@index([position, createdAt], map: "Category_position_createdAt_idx")
  @@map("category")
}

enum CategoryPosition {
  top
  bottom
}
