<!DOCTYPE html>
<html>
<head>
    <title>Session Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .valid { background-color: #d4edda; color: #155724; }
        .invalid { background-color: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Session Validity Test</h1>
    <div id="status" class="status">Checking...</div>
    <button onclick="checkSession()">Check Session</button>
    <button onclick="startAutoCheck()">Start Auto Check (5s)</button>
    <button onclick="stopAutoCheck()">Stop Auto Check</button>
    
    <div id="log" style="margin-top: 20px; padding: 10px; background: #f5f5f5; height: 300px; overflow-y: scroll;"></div>

    <script>
        let autoCheckInterval = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        async function checkSession() {
            try {
                const response = await fetch('/api/auth/session-check');
                const result = await response.json();
                
                const statusDiv = document.getElementById('status');
                
                if (result.valid) {
                    statusDiv.className = 'status valid';
                    statusDiv.textContent = `Session Valid - User: ${result.session.user.email} (${result.session.user.role})`;
                    log(`✅ Session valid for ${result.session.user.email}`);
                } else {
                    statusDiv.className = 'status invalid';
                    statusDiv.textContent = `Session Invalid - ${result.reason || result.error || 'Unknown reason'}`;
                    log(`❌ Session invalid: ${result.reason || result.error || 'Unknown reason'}`);
                    
                    // If session is invalid, redirect to login
                    if (!result.valid) {
                        log('🔄 Redirecting to login...');
                        setTimeout(() => {
                            window.location.href = '/sign-in';
                        }, 2000);
                    }
                }
            } catch (error) {
                const statusDiv = document.getElementById('status');
                statusDiv.className = 'status invalid';
                statusDiv.textContent = `Error: ${error.message}`;
                log(`💥 Error checking session: ${error.message}`);
            }
        }
        
        function startAutoCheck() {
            if (autoCheckInterval) {
                clearInterval(autoCheckInterval);
            }
            
            autoCheckInterval = setInterval(checkSession, 5000);
            log('🔄 Started auto-checking every 5 seconds');
            checkSession(); // Check immediately
        }
        
        function stopAutoCheck() {
            if (autoCheckInterval) {
                clearInterval(autoCheckInterval);
                autoCheckInterval = null;
                log('⏹️ Stopped auto-checking');
            }
        }
        
        // Setup BroadcastChannel listener
        if ('BroadcastChannel' in window) {
            const channel = new BroadcastChannel('session-invalidation');
            channel.addEventListener('message', (event) => {
                if (event.data.type === 'SESSION_INVALIDATED') {
                    log(`📢 Received session invalidation broadcast for user ${event.data.userId}`);
                    checkSession();
                }
            });
            log('📡 BroadcastChannel listener setup complete');
        } else {
            log('⚠️ BroadcastChannel not supported in this browser');
        }
        
        // Initial check
        checkSession();
    </script>
</body>
</html>
