model CancelRequest {
  id           Int       @id @default(autoincrement())
  orderId      Int
  userId       Int
  reason       String
  status       String    @default("pending")
  refundAmount Float?
  adminNotes   String?
  processedBy  Int?
  processedAt  DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  order        NewOrder  @relation(fields: [orderId], references: [id])
  user         User      @relation(fields: [userId], references: [id])

  @@index([orderId], map: "CancelRequest_orderId_fkey")
  @@index([userId], map: "CancelRequest_userId_fkey")
  @@index([createdAt], map: "CancelRequest_createdAt_idx")
  @@index([status], map: "CancelRequest_status_idx")
  @@map("cancelrequest")
}
