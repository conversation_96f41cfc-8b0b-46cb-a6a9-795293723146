model ActivityLog {
  id        Int      @id @default(autoincrement())
  userId    Int?
  username  String?
  action    String
  details   String   @db.Text
  ipAddress String?
  userAgent String?  @db.Text
  metadata  Json?
  createdAt DateTime @default(now())

  // Relations
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  // Indexes
  @@index([userId])
  @@index([action])
  @@index([createdAt])
  @@map("activitylog")
}
