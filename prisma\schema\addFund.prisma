model AddFund {
  id               Int                       @id @default(autoincrement())
  invoice_id       String                    @unique(map: "AddFund_invoice_id_key")
  amount           Float                     // Amount in USD (base currency)
  original_amount  Float?                    // Original amount in user's currency
  email            String
  name             String?
  status           String?                   @default("Processing")
  order_id         String?
  method           String?
  payment_method   String?
  sender_number    String?
  transaction_id   String?
  date             DateTime?
  paid_at          DateTime                  @default(now())
  createdAt        DateTime                  @default(now())
  updatedAt        DateTime                  @default(now()) @updatedAt
  userId           Int
  fee              Float?
  spent_amount     Float
  admin_status     String?                   @default("Pending")
  transaction_type AddFund_transaction_type? @default(deposit)
  reference_id     String?                   @db.VarChar(255)
  currency         String?                   @default("BDT") @db.VarChar(3)
  user             User                      @relation(fields: [userId], references: [id])

  @@index([userId], map: "addfund_userId_fkey")
  @@map("addfund")
}
